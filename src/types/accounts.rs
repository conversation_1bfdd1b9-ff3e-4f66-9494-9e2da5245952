use serde::{Deserialize, Serialize};
use solana_short_vec as short_vec;
use tsify::Tsify;

#[derive(Serialize, Deserialize, Debug, <PERSON>ial<PERSON>q, Eq, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ial<PERSON>rd, Or<PERSON>, <PERSON>h, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
#[repr(transparent)]
pub struct Pubkey([u8; 32]);

impl Pubkey {
    pub const fn new_from_array(pubkey: [u8; 32]) -> Self {
        Self(pubkey)
    }

    pub fn to_bytes(self) -> [u8; 32] {
        self.0
    }
}

impl AsRef<[u8]> for Pubkey {
    fn as_ref(&self) -> &[u8] {
        &self.0[..]
    }
}

impl From<[u8; 32]> for Pubkey {
    fn from(from: [u8; 32]) -> Self {
        Self(from)
    }
}

#[derive(Debug, PartialEq, Eq, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>rd, Or<PERSON>, <PERSON>h, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
#[repr(transparent)]
pub struct Signature([u8; 64]);

impl Signature {
    pub const fn new_from_array(signature: [u8; 64]) -> Self {
        Self(signature)
    }

    pub fn to_bytes(self) -> [u8; 64] {
        self.0
    }
}

impl Default for Signature {
    fn default() -> Self {
        Self([0u8; 64])
    }
}

impl AsRef<[u8]> for Signature {
    fn as_ref(&self) -> &[u8] {
        &self.0[..]
    }
}

impl From<[u8; 64]> for Signature {
    fn from(from: [u8; 64]) -> Self {
        Self(from)
    }
}

impl From<solana_signature::Signature> for Signature {
    fn from(sig: solana_signature::Signature) -> Self {
        Self(sig.as_ref().try_into().unwrap())
    }
}

impl From<Signature> for solana_signature::Signature {
    fn from(sig: Signature) -> Self {
        solana_signature::Signature::from(sig.0)
    }
}

impl Serialize for Signature {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let solana_sig = solana_signature::Signature::from(self.0);
        solana_sig.serialize(serializer)
    }
}

impl<'de> Deserialize<'de> for Signature {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let solana_sig = solana_signature::Signature::deserialize(deserializer)?;
        Ok(Self::from(solana_sig))
    }
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct MessageAddressTableLookup {
    pub account_key: Pubkey,
    #[serde(with = "short_vec")]
    pub writable_indexes: Vec<u8>,
    #[serde(with = "short_vec")]
    pub readonly_indexes: Vec<u8>,
}
