/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const decode_entries: (a: bigint, b: number, c: number) => [number, number, number];
export const __wbg_hash_free: (a: number, b: number) => void;
export const hash_constructor: (a: any) => [number, number, number];
export const hash_toString: (a: number) => [number, number];
export const hash_equals: (a: number, b: number) => number;
export const hash_toBytes: (a: number) => [number, number];
export const __wbindgen_exn_store: (a: number) => void;
export const __externref_table_alloc: () => number;
export const __wbindgen_export_2: WebAssembly.Table;
export const __wbindgen_malloc: (a: number, b: number) => number;
export const __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
export const __externref_table_dealloc: (a: number) => void;
export const __wbindgen_free: (a: number, b: number, c: number) => void;
export const __wbindgen_start: () => void;
