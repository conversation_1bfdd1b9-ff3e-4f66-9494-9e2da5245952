use serde::{Deserialize, Serialize};
use solana_short_vec as short_vec;
use tsify::Tsify;

use super::accounts::Signature;
use super::messages::VersionedMessage;

#[derive(Serialize, Deserialize, Debug, <PERSON>ialEq, De<PERSON>ult, Eq, Clone, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct VersionedTransaction {
    #[serde(with = "short_vec")]
    pub signatures: Vec<Signature>,
    pub message: VersionedMessage,
}
