use serde::ser::SerializeTuple;
use serde::{Deserialize, Deserializer, Serialize, Serializer};
use solana_hash::Hash;
use solana_short_vec as short_vec;
use tsify::Tsify;

use super::accounts::{MessageAddressTableLookup, Pubkey};
use super::instructions::CompiledInstruction;
use crate::utils::message_deserializer::MessageVisitor;

pub const MESSAGE_VERSION_PREFIX: u8 = 0x80;

#[derive(Debug, PartialEq, Eq, Clone, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub enum VersionedMessage {
    Legacy(LegacyMessage),
    V0(V0Message),
}

impl Default for VersionedMessage {
    fn default() -> Self {
        Self::Legacy(LegacyMessage::default())
    }
}

impl Serialize for VersionedMessage {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match self {
            Self::Legacy(message) => {
                let mut seq = serializer.serialize_tuple(1)?;
                seq.serialize_element(message)?;
                seq.end()
            }
            Self::V0(message) => {
                let mut seq = serializer.serialize_tuple(2)?;
                seq.serialize_element(&MESSAGE_VERSION_PREFIX)?;
                seq.serialize_element(message)?;
                seq.end()
            }
        }
    }
}

impl<'de> Deserialize<'de> for VersionedMessage {
    fn deserialize<D>(deserializer: D) -> Result<VersionedMessage, D::Error>
    where
        D: Deserializer<'de>,
    {
        deserializer.deserialize_tuple(2, MessageVisitor)
    }
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct LegacyMessage {
    pub header: MessageHeader,
    #[serde(with = "short_vec")]
    #[tsify(type = "Pubkey[]")]
    pub account_keys: Vec<Pubkey>,
    pub recent_blockhash: Hash,
    #[serde(with = "short_vec")]
    #[tsify(type = "CompiledInstruction[]")]
    pub instructions: Vec<CompiledInstruction>,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct V0Message {
    pub header: MessageHeader,
    #[serde(with = "short_vec")]
    #[tsify(type = "Pubkey[]")]
    pub account_keys: Vec<Pubkey>,
    pub recent_blockhash: Hash,
    #[serde(with = "short_vec")]
    #[tsify(type = "CompiledInstruction[]")]
    pub instructions: Vec<CompiledInstruction>,
    #[serde(with = "short_vec")]
    #[tsify(type = "MessageAddressTableLookup[]")]
    pub address_table_lookups: Vec<MessageAddressTableLookup>,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct MessageHeader {
    pub num_required_signatures: u8,
    pub num_readonly_signed_accounts: u8,
    pub num_readonly_unsigned_accounts: u8,
}
