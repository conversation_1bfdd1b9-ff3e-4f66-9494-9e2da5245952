mod common;

use shredstream_decoder::types::Entry;

use common::mock_data::generators::{
    generate_mock_entry_with_index, generate_mock_entry_with_transactions, generate_mock_parsed_entry_with_entries,
    generate_mock_parsed_entry_with_index,
};

mod entry_tests {
    use super::*;
    use shredstream_decoder::types::{
        CompiledInstruction, LegacyMessage, MessageAddressTableLookup, MessageHeader, Pubkey, Signature, V0Message,
        VersionedMessage, VersionedTransaction,
    };

    use solana_hash::Hash;

    #[test]
    fn test_entry_basic_structure() {
        let entry = Entry { num_hashes: 42, hash: Hash::new_from_array([1u8; 32]), transactions: vec![] };

        assert_eq!(entry.num_hashes, 42);
        assert_eq!(entry.hash, Hash::new_from_array([1u8; 32]));
        assert!(entry.transactions.is_empty());
    }

    #[test]
    fn test_entry_default() {
        let entry = Entry::default();

        assert_eq!(entry.num_hashes, 0);
        assert_eq!(entry.hash, Hash::default());
        assert!(entry.transactions.is_empty());
    }

    #[test]
    fn test_entry_clone_and_equality() {
        let transaction = VersionedTransaction {
            signatures: vec![Signature::new_from_array([42u8; 64])],
            message: VersionedMessage::Legacy(LegacyMessage {
                header: MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 1,
                },
                account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                recent_blockhash: Hash::new_from_array([2u8; 32]),
                instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![0], data: vec![1, 2, 3] }],
            }),
        };

        let entry1 = Entry { num_hashes: 100, hash: Hash::new_from_array([50u8; 32]), transactions: vec![transaction] };
        let entry2 = entry1.clone();

        assert_eq!(entry1, entry2);
        assert_eq!(entry1.num_hashes, entry2.num_hashes);
        assert_eq!(entry1.hash, entry2.hash);
        assert_eq!(entry1.transactions, entry2.transactions);
    }

    #[test]
    fn test_entry_serialization_roundtrip() {
        let transaction = VersionedTransaction {
            signatures: vec![Signature::new_from_array([123u8; 64])],
            message: VersionedMessage::V0(V0Message {
                header: MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 2,
                },
                account_keys: vec![Pubkey::new_from_array([10u8; 32]), Pubkey::new_from_array([20u8; 32])],
                recent_blockhash: Hash::new_from_array([30u8; 32]),
                instructions: vec![CompiledInstruction {
                    program_id_index: 1,
                    accounts: vec![0, 1],
                    data: vec![100, 200],
                }],
                address_table_lookups: vec![MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([40u8; 32]),
                    writable_indexes: vec![0],
                    readonly_indexes: vec![1, 2],
                }],
            }),
        };

        let entry = Entry { num_hashes: 999, hash: Hash::new_from_array([200u8; 32]), transactions: vec![transaction] };

        let serialized = bincode::serialize(&entry).unwrap();
        let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(entry, deserialized);
        assert_eq!(entry.num_hashes, deserialized.num_hashes);
        assert_eq!(entry.hash, deserialized.hash);
        assert_eq!(entry.transactions, deserialized.transactions);
    }

    #[test]
    fn test_entry_empty_transactions() {
        let entry = Entry { num_hashes: 5, hash: Hash::new_from_array([100u8; 32]), transactions: vec![] };

        let serialized = bincode::serialize(&entry).unwrap();
        let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(entry, deserialized);
        assert!(entry.transactions.is_empty());
        assert!(deserialized.transactions.is_empty());
    }

    #[test]
    fn test_entry_multiple_transactions() {
        let transactions = vec![
            VersionedTransaction {
                signatures: vec![Signature::new_from_array([1u8; 64])],
                message: VersionedMessage::Legacy(LegacyMessage::default()),
            },
            VersionedTransaction {
                signatures: vec![Signature::new_from_array([2u8; 64])],
                message: VersionedMessage::Legacy(LegacyMessage::default()),
            },
            VersionedTransaction {
                signatures: vec![Signature::new_from_array([3u8; 64])],
                message: VersionedMessage::Legacy(LegacyMessage::default()),
            },
        ];

        let entry =
            Entry { num_hashes: 777, hash: Hash::new_from_array([150u8; 32]), transactions: transactions.clone() };

        let serialized = bincode::serialize(&entry).unwrap();
        let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(entry, deserialized);
        assert_eq!(entry.transactions.len(), 3);
        assert_eq!(deserialized.transactions.len(), 3);
        assert_eq!(entry.transactions, transactions);
        assert_eq!(deserialized.transactions, transactions);
    }

    #[test]
    fn test_entry_with_mock_generators() {
        let entry = generate_mock_entry_with_transactions(2);

        assert_eq!(entry.transactions.len(), 2);
        assert_eq!(entry.num_hashes, 1);

        let serialized = bincode::serialize(&entry).unwrap();
        let deserialized: Entry = bincode::deserialize(&serialized).unwrap();
        assert_eq!(entry, deserialized);
    }

    #[test]
    fn test_entry_with_seed_generators() {
        let entry1 = generate_mock_entry_with_index(100);
        let entry2 = generate_mock_entry_with_index(100);
        let entry3 = generate_mock_entry_with_index(200);

        // Same seed should produce same result
        assert_eq!(entry1, entry2);
        // Different seed should produce different result
        assert_ne!(entry1, entry3);

        // All should serialize/deserialize correctly
        for entry in [&entry1, &entry2, &entry3] {
            let serialized = bincode::serialize(entry).unwrap();
            let deserialized: Entry = bincode::deserialize(&serialized).unwrap();
            assert_eq!(*entry, deserialized);
        }
    }

    #[test]
    fn test_entry_hash_compatibility() {
        // Test with various hash scenarios
        let hash_scenarios = vec![
            Hash::default(),                   // Default hash
            Hash::new_from_array([0u8; 32]),   // Zero hash
            Hash::new_from_array([255u8; 32]), // Max hash
            Hash::new_from_array([42u8; 32]),  // Custom hash
        ];

        for hash in hash_scenarios {
            let entry = Entry { num_hashes: 1, hash, transactions: vec![] };

            let serialized = bincode::serialize(&entry).unwrap();
            let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

            assert_eq!(entry.hash, hash);
            assert_eq!(deserialized.hash, hash);
            assert_eq!(entry, deserialized);
        }
    }

    #[test]
    fn test_entry_num_hashes_compatibility() {
        // Test with various num_hashes scenarios
        let num_hashes_scenarios = vec![
            0,        // Zero hashes
            1,        // Single hash
            100,      // Medium number
            u64::MAX, // Maximum value
        ];

        for num_hashes in num_hashes_scenarios {
            let entry = Entry { num_hashes, hash: Hash::new_from_array([42u8; 32]), transactions: vec![] };

            let serialized = bincode::serialize(&entry).unwrap();
            let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

            assert_eq!(entry.num_hashes, num_hashes);
            assert_eq!(deserialized.num_hashes, num_hashes);
            assert_eq!(entry, deserialized);
        }
    }

    #[test]
    fn test_entry_transaction_compatibility() {
        // Test with both Legacy and V0 transaction types
        let legacy_transaction = VersionedTransaction {
            signatures: vec![Signature::new_from_array([100u8; 64])],
            message: VersionedMessage::Legacy(LegacyMessage {
                header: MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 2,
                },
                account_keys: vec![Pubkey::new_from_array([10u8; 32]), Pubkey::new_from_array([20u8; 32])],
                recent_blockhash: Hash::new_from_array([30u8; 32]),
                instructions: vec![CompiledInstruction { program_id_index: 0, accounts: vec![1], data: vec![40, 50] }],
            }),
        };

        let v0_transaction = VersionedTransaction {
            signatures: vec![Signature::new_from_array([200u8; 64])],
            message: VersionedMessage::V0(V0Message {
                header: MessageHeader {
                    num_required_signatures: 1,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 1,
                },
                account_keys: vec![Pubkey::new_from_array([60u8; 32])],
                recent_blockhash: Hash::new_from_array([70u8; 32]),
                instructions: vec![CompiledInstruction {
                    program_id_index: 0,
                    accounts: vec![],
                    data: vec![80, 90, 100],
                }],
                address_table_lookups: vec![MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([110u8; 32]),
                    writable_indexes: vec![0],
                    readonly_indexes: vec![1, 2],
                }],
            }),
        };

        let transactions = vec![legacy_transaction, v0_transaction];

        for transaction in transactions {
            let entry = Entry {
                num_hashes: 50,
                hash: Hash::new_from_array([150u8; 32]),
                transactions: vec![transaction.clone()],
            };

            let serialized = bincode::serialize(&entry).unwrap();
            let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

            assert_eq!(entry.transactions.len(), 1);
            assert_eq!(deserialized.transactions.len(), 1);
            assert_eq!(entry.transactions[0], transaction);
            assert_eq!(deserialized.transactions[0], transaction);
            assert_eq!(entry, deserialized);
        }
    }

    #[test]
    fn test_entry_edge_cases() {
        // Test with maximum transactions (reasonable limit for testing)
        let max_transactions: Vec<VersionedTransaction> = (0..20)
            .map(|i| VersionedTransaction {
                signatures: vec![Signature::new_from_array([i as u8; 64])],
                message: VersionedMessage::Legacy(LegacyMessage::default()),
            })
            .collect();

        let entry = Entry {
            num_hashes: u64::MAX,
            hash: Hash::new_from_array([255u8; 32]),
            transactions: max_transactions.clone(),
        };

        let serialized = bincode::serialize(&entry).unwrap();
        let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(entry, deserialized);
        assert_eq!(entry.transactions.len(), 20);
        assert_eq!(deserialized.transactions.len(), 20);
        assert_eq!(entry.transactions, max_transactions);
        assert_eq!(entry.num_hashes, u64::MAX);

        // Test with zero num_hashes and complex transactions
        let complex_transactions = vec![VersionedTransaction {
            signatures: vec![Signature::new_from_array([1u8; 64]), Signature::new_from_array([2u8; 64])],
            message: VersionedMessage::V0(V0Message {
                header: MessageHeader {
                    num_required_signatures: 2,
                    num_readonly_signed_accounts: 1,
                    num_readonly_unsigned_accounts: 3,
                },
                account_keys: (0..4).map(|i| Pubkey::new_from_array([i as u8; 32])).collect(),
                recent_blockhash: Hash::new_from_array([100u8; 32]),
                instructions: (0..3)
                    .map(|i| CompiledInstruction {
                        program_id_index: i,
                        accounts: vec![i, i + 1],
                        data: vec![i as u8, (i + 1) as u8],
                    })
                    .collect(),
                address_table_lookups: vec![MessageAddressTableLookup {
                    account_key: Pubkey::new_from_array([200u8; 32]),
                    writable_indexes: vec![0, 1],
                    readonly_indexes: vec![2, 3, 4],
                }],
            }),
        }];

        let zero_hash_entry = Entry { num_hashes: 0, hash: Hash::default(), transactions: complex_transactions };

        let serialized = bincode::serialize(&zero_hash_entry).unwrap();
        let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(zero_hash_entry, deserialized);
        assert_eq!(zero_hash_entry.num_hashes, 0);
        assert_eq!(zero_hash_entry.hash, Hash::default());
        assert_eq!(zero_hash_entry.transactions.len(), 1);
    }
}

mod parsed_entry_tests {
    use super::{generate_mock_parsed_entry_with_entries, generate_mock_parsed_entry_with_index};
    use shredstream_decoder::types::{
        Entry, LegacyMessage, MessageHeader, ParsedEntry, Pubkey, Signature, VersionedMessage, VersionedTransaction,
    };
    use solana_hash::Hash;

    #[test]
    fn test_parsed_entry_basic_structure() {
        let entry = Entry { num_hashes: 42, hash: Hash::new_from_array([1u8; 32]), transactions: vec![] };

        let parsed_entry = ParsedEntry { slot: 12345, entries: vec![entry] };

        assert_eq!(parsed_entry.slot, 12345);
        assert_eq!(parsed_entry.entries.len(), 1);
        assert_eq!(parsed_entry.entries[0].num_hashes, 42);
    }

    #[test]
    fn test_parsed_entry_clone_and_equality() {
        let entry = Entry {
            num_hashes: 100,
            hash: Hash::new_from_array([50u8; 32]),
            transactions: vec![VersionedTransaction {
                signatures: vec![Signature::new_from_array([42u8; 64])],
                message: VersionedMessage::Legacy(LegacyMessage {
                    header: MessageHeader {
                        num_required_signatures: 1,
                        num_readonly_signed_accounts: 0,
                        num_readonly_unsigned_accounts: 1,
                    },
                    account_keys: vec![Pubkey::new_from_array([1u8; 32])],
                    recent_blockhash: Hash::new_from_array([2u8; 32]),
                    instructions: vec![],
                }),
            }],
        };

        let parsed_entry1 = ParsedEntry { slot: 99999, entries: vec![entry] };
        let parsed_entry2 = parsed_entry1.clone();

        assert_eq!(parsed_entry1, parsed_entry2);
        assert_eq!(parsed_entry1.slot, parsed_entry2.slot);
        assert_eq!(parsed_entry1.entries, parsed_entry2.entries);
    }

    #[test]
    fn test_parsed_entry_serialization_roundtrip() {
        let entries = vec![
            Entry { num_hashes: 1, hash: Hash::new_from_array([10u8; 32]), transactions: vec![] },
            Entry {
                num_hashes: 2,
                hash: Hash::new_from_array([20u8; 32]),
                transactions: vec![VersionedTransaction {
                    signatures: vec![Signature::new_from_array([123u8; 64])],
                    message: VersionedMessage::Legacy(LegacyMessage::default()),
                }],
            },
        ];

        let parsed_entry = ParsedEntry { slot: 777777, entries };

        let serialized = bincode::serialize(&parsed_entry).unwrap();
        let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(parsed_entry, deserialized);
        assert_eq!(parsed_entry.slot, deserialized.slot);
        assert_eq!(parsed_entry.entries, deserialized.entries);
        assert_eq!(parsed_entry.entries.len(), 2);
        assert_eq!(deserialized.entries.len(), 2);
    }

    #[test]
    fn test_parsed_entry_empty_entries() {
        let parsed_entry = ParsedEntry { slot: 555, entries: vec![] };

        let serialized = bincode::serialize(&parsed_entry).unwrap();
        let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(parsed_entry, deserialized);
        assert_eq!(parsed_entry.slot, 555);
        assert!(parsed_entry.entries.is_empty());
        assert!(deserialized.entries.is_empty());
    }

    #[test]
    fn test_parsed_entry_multiple_entries() {
        let entries = vec![
            Entry { num_hashes: 10, hash: Hash::new_from_array([100u8; 32]), transactions: vec![] },
            Entry {
                num_hashes: 20,
                hash: Hash::new_from_array([200u8; 32]),
                transactions: vec![VersionedTransaction {
                    signatures: vec![Signature::new_from_array([1u8; 64])],
                    message: VersionedMessage::Legacy(LegacyMessage::default()),
                }],
            },
            Entry {
                num_hashes: 30,
                hash: Hash::new_from_array([255u8; 32]),
                transactions: vec![
                    VersionedTransaction {
                        signatures: vec![Signature::new_from_array([2u8; 64])],
                        message: VersionedMessage::Legacy(LegacyMessage::default()),
                    },
                    VersionedTransaction {
                        signatures: vec![Signature::new_from_array([3u8; 64])],
                        message: VersionedMessage::Legacy(LegacyMessage::default()),
                    },
                ],
            },
        ];

        let parsed_entry = ParsedEntry { slot: 888888, entries: entries.clone() };

        let serialized = bincode::serialize(&parsed_entry).unwrap();
        let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(parsed_entry, deserialized);
        assert_eq!(parsed_entry.entries.len(), 3);
        assert_eq!(deserialized.entries.len(), 3);
        assert_eq!(parsed_entry.entries, entries);
        assert_eq!(deserialized.entries, entries);

        // Verify individual entries
        assert_eq!(parsed_entry.entries[0].transactions.len(), 0);
        assert_eq!(parsed_entry.entries[1].transactions.len(), 1);
        assert_eq!(parsed_entry.entries[2].transactions.len(), 2);
    }

    #[test]
    fn test_parsed_entry_with_mock_generators() {
        let parsed_entry = generate_mock_parsed_entry_with_entries(3);

        assert_eq!(parsed_entry.entries.len(), 3);
        assert!(parsed_entry.slot > 0);

        let serialized = bincode::serialize(&parsed_entry).unwrap();
        let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();
        assert_eq!(parsed_entry, deserialized);
    }

    #[test]
    fn test_parsed_entry_with_seed_generators() {
        let parsed_entry1 = generate_mock_parsed_entry_with_index(500);
        let parsed_entry2 = generate_mock_parsed_entry_with_index(500);
        let parsed_entry3 = generate_mock_parsed_entry_with_index(600);

        // Same seed should produce same result
        assert_eq!(parsed_entry1, parsed_entry2);
        // Different seed should produce different result
        assert_ne!(parsed_entry1, parsed_entry3);

        // All should serialize/deserialize correctly
        for parsed_entry in [&parsed_entry1, &parsed_entry2, &parsed_entry3] {
            let serialized = bincode::serialize(parsed_entry).unwrap();
            let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();
            assert_eq!(*parsed_entry, deserialized);
        }
    }

    #[test]
    fn test_parsed_entry_slot_compatibility() {
        // Test with various slot scenarios
        let slot_scenarios = vec![
            0,        // Zero slot
            1,        // Single slot
            12345,    // Medium slot
            u64::MAX, // Maximum slot
        ];

        for slot in slot_scenarios {
            let parsed_entry = ParsedEntry { slot, entries: vec![Entry::default()] };

            let serialized = bincode::serialize(&parsed_entry).unwrap();
            let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();

            assert_eq!(parsed_entry.slot, slot);
            assert_eq!(deserialized.slot, slot);
            assert_eq!(parsed_entry, deserialized);
        }
    }

    #[test]
    fn test_parsed_entry_entries_compatibility() {
        use shredstream_decoder::types::{CompiledInstruction, MessageAddressTableLookup, V0Message};

        // Test with various entry scenarios
        let entry_scenarios = vec![
            // Empty entries
            vec![],
            // Single entry with no transactions
            vec![Entry::default()],
            // Single entry with transactions
            vec![Entry {
                num_hashes: 42,
                hash: Hash::new_from_array([42u8; 32]),
                transactions: vec![VersionedTransaction {
                    signatures: vec![Signature::new_from_array([42u8; 64])],
                    message: VersionedMessage::Legacy(LegacyMessage::default()),
                }],
            }],
            // Multiple entries with mixed content
            vec![
                Entry::default(),
                Entry { num_hashes: 100, hash: Hash::new_from_array([100u8; 32]), transactions: vec![] },
                Entry {
                    num_hashes: 200,
                    hash: Hash::new_from_array([200u8; 32]),
                    transactions: vec![
                        VersionedTransaction {
                            signatures: vec![Signature::new_from_array([1u8; 64])],
                            message: VersionedMessage::Legacy(LegacyMessage::default()),
                        },
                        VersionedTransaction {
                            signatures: vec![Signature::new_from_array([2u8; 64])],
                            message: VersionedMessage::V0(V0Message {
                                header: MessageHeader {
                                    num_required_signatures: 1,
                                    num_readonly_signed_accounts: 0,
                                    num_readonly_unsigned_accounts: 1,
                                },
                                account_keys: vec![Pubkey::new_from_array([50u8; 32])],
                                recent_blockhash: Hash::new_from_array([60u8; 32]),
                                instructions: vec![CompiledInstruction {
                                    program_id_index: 0,
                                    accounts: vec![],
                                    data: vec![70, 80],
                                }],
                                address_table_lookups: vec![MessageAddressTableLookup {
                                    account_key: Pubkey::new_from_array([90u8; 32]),
                                    writable_indexes: vec![0],
                                    readonly_indexes: vec![1],
                                }],
                            }),
                        },
                    ],
                },
            ],
        ];

        for (i, entries) in entry_scenarios.into_iter().enumerate() {
            let parsed_entry = ParsedEntry { slot: (i + 1) as u64 * 1000, entries: entries.clone() };

            let serialized = bincode::serialize(&parsed_entry).unwrap();
            let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();

            assert_eq!(parsed_entry.entries.len(), entries.len(), "Entry count mismatch in scenario {}", i);
            assert_eq!(
                deserialized.entries.len(),
                entries.len(),
                "Deserialized entry count mismatch in scenario {}",
                i
            );
            assert_eq!(parsed_entry.entries, entries, "Entries mismatch in scenario {}", i);
            assert_eq!(deserialized.entries, entries, "Deserialized entries mismatch in scenario {}", i);
            assert_eq!(parsed_entry, deserialized, "ParsedEntry mismatch in scenario {}", i);
        }
    }

    #[test]
    fn test_parsed_entry_edge_cases() {
        // Test with maximum entries (reasonable limit for testing)
        let max_entries: Vec<Entry> = (0..50)
            .map(|i| Entry {
                num_hashes: i as u64,
                hash: Hash::new_from_array([i as u8; 32]),
                transactions: if i % 3 == 0 {
                    vec![VersionedTransaction {
                        signatures: vec![Signature::new_from_array([i as u8; 64])],
                        message: VersionedMessage::Legacy(LegacyMessage::default()),
                    }]
                } else {
                    vec![]
                },
            })
            .collect();

        let parsed_entry = ParsedEntry { slot: u64::MAX, entries: max_entries.clone() };

        let serialized = bincode::serialize(&parsed_entry).unwrap();
        let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(parsed_entry, deserialized);
        assert_eq!(parsed_entry.entries.len(), 50);
        assert_eq!(deserialized.entries.len(), 50);
        assert_eq!(parsed_entry.entries, max_entries);
        assert_eq!(parsed_entry.slot, u64::MAX);

        // Verify some entries have transactions, some don't
        let entries_with_transactions = parsed_entry.entries.iter().filter(|e| !e.transactions.is_empty()).count();
        assert!(entries_with_transactions > 0);
        assert!(entries_with_transactions < 50);
    }

    #[test]
    fn test_parsed_entry_comprehensive_roundtrip() {
        // Test comprehensive scenarios with different combinations
        let test_cases = vec![
            // Case 1: Empty entries
            ParsedEntry { slot: 0, entries: vec![] },
            // Case 2: Single entry with no transactions
            ParsedEntry { slot: 12345, entries: vec![Entry::default()] },
            // Case 3: Single entry with single transaction
            ParsedEntry {
                slot: 67890,
                entries: vec![Entry {
                    num_hashes: 42,
                    hash: Hash::new_from_array([42u8; 32]),
                    transactions: vec![VersionedTransaction {
                        signatures: vec![Signature::new_from_array([42u8; 64])],
                        message: VersionedMessage::Legacy(LegacyMessage::default()),
                    }],
                }],
            },
            // Case 4: Multiple entries with mixed transactions
            ParsedEntry {
                slot: u64::MAX,
                entries: vec![
                    Entry { num_hashes: 1, hash: Hash::new_from_array([1u8; 32]), transactions: vec![] },
                    Entry {
                        num_hashes: 2,
                        hash: Hash::new_from_array([2u8; 32]),
                        transactions: vec![VersionedTransaction {
                            signatures: vec![Signature::new_from_array([10u8; 64])],
                            message: VersionedMessage::Legacy(LegacyMessage::default()),
                        }],
                    },
                    Entry {
                        num_hashes: 3,
                        hash: Hash::new_from_array([3u8; 32]),
                        transactions: vec![
                            VersionedTransaction {
                                signatures: vec![Signature::new_from_array([20u8; 64])],
                                message: VersionedMessage::Legacy(LegacyMessage::default()),
                            },
                            VersionedTransaction {
                                signatures: vec![Signature::new_from_array([30u8; 64])],
                                message: VersionedMessage::Legacy(LegacyMessage::default()),
                            },
                        ],
                    },
                ],
            },
        ];

        for (i, parsed_entry) in test_cases.into_iter().enumerate() {
            let serialized = bincode::serialize(&parsed_entry).unwrap();
            let deserialized: ParsedEntry = bincode::deserialize(&serialized).unwrap();

            assert_eq!(parsed_entry, deserialized, "Test case {} failed", i);
            assert_eq!(parsed_entry.slot, deserialized.slot, "Slot mismatch in test case {}", i);
            assert_eq!(parsed_entry.entries, deserialized.entries, "Entries mismatch in test case {}", i);
            assert_eq!(
                parsed_entry.entries.len(),
                deserialized.entries.len(),
                "Entry count mismatch in test case {}",
                i
            );
        }
    }
}
