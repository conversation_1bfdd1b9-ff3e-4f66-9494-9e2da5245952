# TypeScript Test for Shredstream Decoder

Đây là file test TypeScript đơn giản để kiểm tra hàm `decode_entries` từ WASM module.

## Yêu cầu

- Bun runtime
- WASM package đã được build (chạy `make build-wasm` từ thư mục root)

## Cài đặt

```bash
cd test-typescript
bun install
```

## Chạy test

```bash
# Chạy test một lần
bun run test

# Hoặc chạy với watch mode
bun run dev
```

## Cấu trúc test

Test bao gồm các trường hợp sau:

1. **Test với dữ liệu rỗng** - Kiểm tra xử lý lỗi
2. **Test với dữ liệu không hợp lệ** - Kiểm tra xử lý lỗi  
3. **Test với mảng entries rỗng hợp lệ** - Kiểm tra decode thành công
4. **Test interface TypeScript** - Kiểm tra type safety
5. **Kiểm tra signature function** - <PERSON><PERSON><PERSON> nhận types đúng

## Kết quả mong đợi

- ✅ WASM module khởi tạo thành công
- ✅ Function xử lý lỗi đúng cách với dữ liệu không hợp lệ
- ✅ Function decode thành công với dữ liệu hợp lệ
- ✅ TypeScript types được định nghĩa chính xác
- ✅ Function signature đúng như mong đợi

## Ghi chú

- Test này sử dụng dữ liệu mock đơn giản
- Để test với dữ liệu thực, cần sử dụng bincode serialization từ Rust
- Có thể mở rộng test bằng cách sử dụng dữ liệu từ `tests/data/` directory
