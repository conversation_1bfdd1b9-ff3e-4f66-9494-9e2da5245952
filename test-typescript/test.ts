import { readFileSync } from 'fs'
import { decode_entries } from '../pkg-node/shredstream_decoder.js'

function analyzeDataStructure(obj: any, path: string = '', depth: number = 0): void {
    const indent = '  '.repeat(depth)

    if (obj === null || obj === undefined) {
        console.log(`${indent}${path}: ${obj} (${typeof obj})`)
        return
    }

    if (typeof obj === 'object') {
        if (Array.isArray(obj)) {
            console.log(`${indent}${path}: Array[${obj.length}]`)
            if (obj.length > 0) {
                console.log(`${indent}  [0] type: ${typeof obj[0]}`)
                if (obj.length <= 3) {
                    // Show all elements for small arrays
                    obj.forEach((item, i) => {
                        analyzeDataStructure(item, `[${i}]`, depth + 1)
                    })
                } else {
                    // Show first and last for large arrays
                    analyzeDataStructure(obj[0], '[0]', depth + 1)
                    if (obj.length > 1) {
                        console.log(`${indent}  ... (${obj.length - 2} more items)`)
                        analyzeDataStructure(obj[obj.length - 1], `[${obj.length - 1}]`, depth + 1)
                    }
                }
            }
        } else {
            console.log(`${indent}${path}: Object`)
            for (const [key, value] of Object.entries(obj)) {
                analyzeDataStructure(value, key, depth + 1)
            }
        }
    } else {
        const preview = typeof obj === 'string' && obj.length > 50 ? `"${obj.substring(0, 50)}..."` : obj
        console.log(`${indent}${path}: ${preview} (${typeof obj})`)
    }
}

function testDecodeEntries() {
    console.log('=== DECODING SHRED DATA ===')
    const shred = readFileSync('./tests/data/shred_000001.bin')
    const result = decode_entries(1n, shred)

    console.log('\n=== FULL DATA STRUCTURE ANALYSIS ===')
    analyzeDataStructure(result, 'result')

    console.log('\n=== DETAILED SIGNATURE ANALYSIS ===')
    let entryCount = 0
    for (const entry of result.entries) {
        entryCount++
        console.log(`\nEntry ${entryCount}:`)
        console.log(`  num_hashes: ${entry.num_hashes} (${typeof entry.num_hashes})`)
        console.log(`  hash type: ${typeof entry.hash}`)
        console.log(`  transactions: Array[${entry.transactions.length}]`)

        let txCount = 0
        for (const tx of entry.transactions) {
            txCount++
            console.log(`\n  Transaction ${txCount}:`)

            // Analyze signatures in detail
            console.log(`    signatures type: ${typeof tx.signatures}`)
            console.log(`    signatures.length: ${tx.signatures.length}`)
            console.log(`    signatures Array.isArray: ${Array.isArray(tx.signatures)}`)

            for (let i = 0; i < tx.signatures.length; i++) {
                const sig = tx.signatures[i]
                console.log(`    signatures[${i}]:`)
                console.log(`      type: ${typeof sig}`)
                console.log(`      Array.isArray: ${Array.isArray(sig)}`)
                if (Array.isArray(sig)) {
                    console.log(`      length: ${sig.length}`)
                    console.log(`      first 5 bytes: [${sig.slice(0, 5).join(', ')}]`)
                    console.log(`      last 5 bytes: [${sig.slice(-5).join(', ')}]`)
                } else {
                    console.log(`      value: ${sig}`)
                }
            }

            // Analyze message
            console.log(`    message type: ${typeof tx.message}`)
            if (tx.message && typeof tx.message === 'object') {
                console.log(`    message keys: [${Object.keys(tx.message).join(', ')}]`)
            }

            // Only analyze first transaction in detail to avoid spam
            if (txCount >= 1) break
        }

        // Only analyze first 2 entries in detail
        if (entryCount >= 2) break
    }

    console.log('\n=== TYPE COMPARISON WITH .d.ts ===')
    console.log('Expected from .d.ts:')
    console.log('  result: ParsedEntry')
    console.log('  result.entries: Entry[]')
    console.log('  entry.signatures: Signature[]')
    console.log('  Signature = number[]')
    console.log('')
    console.log('Actual runtime types:')
    console.log(`  result type: ${typeof result}`)
    console.log(`  result.entries type: ${typeof result.entries} (Array: ${Array.isArray(result.entries)})`)
    if (result.entries.length > 0 && result.entries[0].transactions.length > 0) {
        const firstSig = result.entries[0].transactions[0].signatures[0]
        console.log(`  first signature type: ${typeof firstSig} (Array: ${Array.isArray(firstSig)})`)
        if (Array.isArray(firstSig)) {
            console.log(`  first signature length: ${firstSig.length}`)
        }
    }
}

testDecodeEntries()
