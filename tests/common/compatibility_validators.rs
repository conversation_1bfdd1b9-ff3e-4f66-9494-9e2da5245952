use bincode;
use shredstream_decoder::types::{Entry, Signature as CustomSignature, VersionedTransaction};
use solana_entry::entry::Entry as SolanaEntry;
use solana_hash::Hash;
use solana_signature::Signature as SolanaSignature;
use solana_transaction::versioned::VersionedTransaction as SolanaVersionedTransaction;

#[allow(dead_code)]
pub mod validators {
    use super::*;

    #[derive(Debug, PartialEq)]
    pub enum CompatibilityResult {
        Success,
        BinaryMismatch { custom: Vec<u8>, reference: Vec<u8> },
        SerializationError(String),
        DeserializationError(String),
        StructuralMismatch(String),
    }

    pub fn validate_entry_binary_compatibility(custom_bytes: &[u8], reference_bytes: &[u8]) -> bool {
        custom_bytes == reference_bytes
    }

    pub fn validate_transaction_binary_compatibility(custom_bytes: &[u8], reference_bytes: &[u8]) -> bool {
        custom_bytes == reference_bytes
    }

    pub fn validate_message_binary_compatibility(custom_bytes: &[u8], reference_bytes: &[u8]) -> bool {
        custom_bytes == reference_bytes
    }

    pub fn validate_pubkey_binary_compatibility(custom_bytes: &[u8], reference_bytes: &[u8]) -> bool {
        custom_bytes == reference_bytes
    }

    pub fn validate_entry_structure_compatibility(custom: &Entry, reference: &SolanaEntry) -> bool {
        custom.num_hashes == reference.num_hashes
            && custom.hash.as_ref() == reference.hash.as_ref()
            && custom.transactions.len() == reference.transactions.len()
    }

    pub fn validate_transaction_structure_compatibility(
        custom: &VersionedTransaction,
        reference: &SolanaVersionedTransaction,
    ) -> bool {
        custom.signatures.len() == reference.signatures.len()
    }

    pub fn validate_hash_compatibility(custom_hash: &Hash, reference_hash: &Hash) -> bool {
        custom_hash.as_ref() == reference_hash.as_ref()
    }

    pub fn validate_signature_compatibility(custom_sig: &CustomSignature, reference_sig: &SolanaSignature) -> bool {
        custom_sig.as_ref() == reference_sig.as_ref()
    }

    pub fn validate_signature_binary_compatibility(
        custom_sig: &CustomSignature,
        reference_sig: &SolanaSignature,
    ) -> CompatibilityResult {
        let custom_bytes = match bincode::serialize(custom_sig) {
            Ok(bytes) => bytes,
            Err(e) => {
                return CompatibilityResult::SerializationError(format!("Custom signature serialization failed: {}", e))
            }
        };

        let reference_bytes = match bincode::serialize(reference_sig) {
            Ok(bytes) => bytes,
            Err(e) => {
                return CompatibilityResult::SerializationError(format!(
                    "Reference signature serialization failed: {}",
                    e
                ))
            }
        };

        if custom_bytes != reference_bytes {
            return CompatibilityResult::BinaryMismatch { custom: custom_bytes, reference: reference_bytes };
        }

        CompatibilityResult::Success
    }

    pub fn validate_100_percent_compatibility<T, R>(
        custom_data: &T,
        reference_data: &R,
        test_bytes: &[u8],
    ) -> CompatibilityResult
    where
        T: serde::Serialize + for<'de> serde::Deserialize<'de>,
        R: serde::Serialize + for<'de> serde::Deserialize<'de>,
    {
        let custom_serialized = match bincode::serialize(custom_data) {
            Ok(bytes) => bytes,
            Err(e) => return CompatibilityResult::SerializationError(format!("Custom serialization failed: {}", e)),
        };

        let reference_serialized = match bincode::serialize(reference_data) {
            Ok(bytes) => bytes,
            Err(e) => return CompatibilityResult::SerializationError(format!("Reference serialization failed: {}", e)),
        };

        if custom_serialized != reference_serialized {
            return CompatibilityResult::BinaryMismatch { custom: custom_serialized, reference: reference_serialized };
        }

        let custom_deserialized: T = match bincode::deserialize(test_bytes) {
            Ok(data) => data,
            Err(e) => {
                return CompatibilityResult::DeserializationError(format!("Custom deserialization failed: {}", e))
            }
        };

        let reference_deserialized: R = match bincode::deserialize(test_bytes) {
            Ok(data) => data,
            Err(e) => {
                return CompatibilityResult::DeserializationError(format!("Reference deserialization failed: {}", e))
            }
        };

        let custom_roundtrip = match bincode::serialize(&custom_deserialized) {
            Ok(bytes) => bytes,
            Err(e) => return CompatibilityResult::SerializationError(format!("Custom roundtrip failed: {}", e)),
        };

        let reference_roundtrip = match bincode::serialize(&reference_deserialized) {
            Ok(bytes) => bytes,
            Err(e) => return CompatibilityResult::SerializationError(format!("Reference roundtrip failed: {}", e)),
        };

        if custom_roundtrip != reference_roundtrip {
            return CompatibilityResult::BinaryMismatch { custom: custom_roundtrip, reference: reference_roundtrip };
        }

        CompatibilityResult::Success
    }

    pub fn validate_serialization_roundtrip<T>(data: &T) -> CompatibilityResult
    where
        T: serde::Serialize + for<'de> serde::Deserialize<'de> + PartialEq + std::fmt::Debug,
    {
        let serialized = match bincode::serialize(data) {
            Ok(bytes) => bytes,
            Err(e) => return CompatibilityResult::SerializationError(e.to_string()),
        };

        let deserialized: T = match bincode::deserialize(&serialized) {
            Ok(data) => data,
            Err(e) => return CompatibilityResult::DeserializationError(e.to_string()),
        };

        if data != &deserialized {
            return CompatibilityResult::StructuralMismatch("Roundtrip data mismatch".to_string());
        }

        CompatibilityResult::Success
    }

    pub fn compare_byte_arrays(a: &[u8], b: &[u8]) -> Option<usize> {
        if a.len() != b.len() {
            return Some(0);
        }

        for (i, (byte_a, byte_b)) in a.iter().zip(b.iter()).enumerate() {
            if byte_a != byte_b {
                return Some(i);
            }
        }

        None
    }

    pub fn generate_compatibility_report<T, R>(custom: &T, reference: &R, test_name: &str) -> String
    where
        T: serde::Serialize,
        R: serde::Serialize,
    {
        let custom_bytes = bincode::serialize(custom).unwrap_or_default();
        let reference_bytes = bincode::serialize(reference).unwrap_or_default();

        let mut report = format!("Compatibility Report for {}\n", test_name);
        report.push_str(&format!("Custom size: {} bytes\n", custom_bytes.len()));
        report.push_str(&format!("Reference size: {} bytes\n", reference_bytes.len()));

        if let Some(diff_pos) = compare_byte_arrays(&custom_bytes, &reference_bytes) {
            report.push_str(&format!("First difference at byte position: {}\n", diff_pos));
            if diff_pos < custom_bytes.len() && diff_pos < reference_bytes.len() {
                report.push_str(&format!("Custom byte: 0x{:02x}\n", custom_bytes[diff_pos]));
                report.push_str(&format!("Reference byte: 0x{:02x}\n", reference_bytes[diff_pos]));
            }
        } else {
            report.push_str("Binary data matches perfectly!\n");
        }

        report
    }
}
