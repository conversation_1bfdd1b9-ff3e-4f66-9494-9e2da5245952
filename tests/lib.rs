mod common;

use common::mock_data::generators::generate_mock_entry_with_transactions;

mod decode_entries_tests {
    use super::*;
    use shredstream_decoder::{
        decode_entries_internal,
        types::{Entry, LegacyMessage, MessageHeader, Pubkey, Signature, VersionedMessage, VersionedTransaction},
    };
    use solana_hash::Hash;

    #[test]
    fn test_decode_entries_basic_functionality() {
        // Create test entries
        let entries = vec![
            Entry { num_hashes: 1, hash: Hash::new_from_array([1u8; 32]), transactions: vec![] },
            Entry {
                num_hashes: 2,
                hash: Hash::new_from_array([2u8; 32]),
                transactions: vec![VersionedTransaction {
                    signatures: vec![Signature::new_from_array([42u8; 64])],
                    message: VersionedMessage::Legacy(LegacyMessage::default()),
                }],
            },
        ];

        // Serialize entries
        let serialized = bincode::serialize(&entries).unwrap();

        // Test decode_entries function
        let slot = 12345;
        let result = decode_entries_internal(slot, &serialized);

        assert!(result.is_ok());
        let parsed_entry = result.unwrap();
        assert_eq!(parsed_entry.slot, slot);
        assert_eq!(parsed_entry.entries.len(), 2);
        assert_eq!(parsed_entry.entries, entries);
    }

    #[test]
    fn test_decode_entries_empty_data() {
        let slot = 999;
        let result = decode_entries_internal(slot, &[]);

        assert!(result.is_err());
        // Note: In WASM context, we can't easily check error message
        // but we can verify it fails as expected
    }

    #[test]
    fn test_decode_entries_invalid_data() {
        let slot = 888;
        let invalid_data = vec![1, 2, 3, 4, 5]; // Invalid bincode data

        let result = decode_entries_internal(slot, &invalid_data);

        assert!(result.is_err());
        // Function should fail gracefully with invalid data
    }

    #[test]
    fn test_decode_entries_empty_entries_array() {
        let entries: Vec<Entry> = vec![];
        let serialized = bincode::serialize(&entries).unwrap();

        let slot = 777;
        let result = decode_entries_internal(slot, &serialized);

        assert!(result.is_ok());
        let parsed_entry = result.unwrap();
        assert_eq!(parsed_entry.slot, slot);
        assert!(parsed_entry.entries.is_empty());
    }

    #[test]
    fn test_decode_entries_single_entry() {
        let entry = Entry {
            num_hashes: 42,
            hash: Hash::new_from_array([42u8; 32]),
            transactions: vec![VersionedTransaction {
                signatures: vec![Signature::new_from_array([100u8; 64])],
                message: VersionedMessage::Legacy(LegacyMessage {
                    header: MessageHeader {
                        num_required_signatures: 1,
                        num_readonly_signed_accounts: 0,
                        num_readonly_unsigned_accounts: 1,
                    },
                    account_keys: vec![Pubkey::new_from_array([50u8; 32])],
                    recent_blockhash: Hash::new_from_array([60u8; 32]),
                    instructions: vec![],
                }),
            }],
        };

        let entries = vec![entry.clone()];
        let serialized = bincode::serialize(&entries).unwrap();

        let slot = 555;
        let result = decode_entries_internal(slot, &serialized);

        assert!(result.is_ok());
        let parsed_entry = result.unwrap();
        assert_eq!(parsed_entry.slot, slot);
        assert_eq!(parsed_entry.entries.len(), 1);
        assert_eq!(parsed_entry.entries[0], entry);
    }

    #[test]
    fn test_decode_entries_multiple_entries() {
        let entries = vec![
            Entry { num_hashes: 10, hash: Hash::new_from_array([10u8; 32]), transactions: vec![] },
            Entry {
                num_hashes: 20,
                hash: Hash::new_from_array([20u8; 32]),
                transactions: vec![VersionedTransaction {
                    signatures: vec![Signature::new_from_array([1u8; 64])],
                    message: VersionedMessage::Legacy(LegacyMessage::default()),
                }],
            },
            Entry {
                num_hashes: 30,
                hash: Hash::new_from_array([30u8; 32]),
                transactions: vec![
                    VersionedTransaction {
                        signatures: vec![Signature::new_from_array([2u8; 64])],
                        message: VersionedMessage::Legacy(LegacyMessage::default()),
                    },
                    VersionedTransaction {
                        signatures: vec![Signature::new_from_array([3u8; 64])],
                        message: VersionedMessage::Legacy(LegacyMessage::default()),
                    },
                ],
            },
        ];

        let serialized = bincode::serialize(&entries).unwrap();

        let slot = 333;
        let result = decode_entries_internal(slot, &serialized);

        assert!(result.is_ok());
        let parsed_entry = result.unwrap();
        assert_eq!(parsed_entry.slot, slot);
        assert_eq!(parsed_entry.entries.len(), 3);
        assert_eq!(parsed_entry.entries, entries);

        // Verify individual entries
        assert_eq!(parsed_entry.entries[0].transactions.len(), 0);
        assert_eq!(parsed_entry.entries[1].transactions.len(), 1);
        assert_eq!(parsed_entry.entries[2].transactions.len(), 2);
    }

    #[test]
    fn test_decode_entries_with_mock_data() {
        let entry = generate_mock_entry_with_transactions(2);
        let entries = vec![entry.clone()];
        let serialized = bincode::serialize(&entries).unwrap();

        let slot = 111;
        let result = decode_entries_internal(slot, &serialized);

        assert!(result.is_ok());
        let parsed_entry = result.unwrap();
        assert_eq!(parsed_entry.slot, slot);
        assert_eq!(parsed_entry.entries.len(), 1);
        assert_eq!(parsed_entry.entries[0], entry);
        assert_eq!(parsed_entry.entries[0].transactions.len(), 2);
    }

    #[test]
    fn test_decode_entries_slot_values() {
        let entries = vec![Entry::default()];
        let serialized = bincode::serialize(&entries).unwrap();

        // Test various slot values
        let slot_values = vec![0, 1, 12345, u64::MAX];

        for slot in slot_values {
            let result = decode_entries_internal(slot, &serialized);
            assert!(result.is_ok());
            let parsed_entry = result.unwrap();
            assert_eq!(parsed_entry.slot, slot);
            assert_eq!(parsed_entry.entries.len(), 1);
        }
    }

    #[test]
    fn test_decode_entries_large_data() {
        // Create a large number of entries
        let mut entries = Vec::new();
        for i in 0..100 {
            entries.push(Entry {
                num_hashes: i as u64,
                hash: Hash::new_from_array([i as u8; 32]),
                transactions: if i % 3 == 0 {
                    vec![VersionedTransaction {
                        signatures: vec![Signature::new_from_array([i as u8; 64])],
                        message: VersionedMessage::Legacy(LegacyMessage::default()),
                    }]
                } else {
                    vec![]
                },
            });
        }

        let serialized = bincode::serialize(&entries).unwrap();

        let slot = 999999;
        let result = decode_entries_internal(slot, &serialized);

        assert!(result.is_ok());
        let parsed_entry = result.unwrap();
        assert_eq!(parsed_entry.slot, slot);
        assert_eq!(parsed_entry.entries.len(), 100);
        assert_eq!(parsed_entry.entries, entries);

        // Verify some entries have transactions, some don't
        let entries_with_transactions = parsed_entry.entries.iter().filter(|e| !e.transactions.is_empty()).count();
        assert!(entries_with_transactions > 0);
        assert!(entries_with_transactions < 100);
    }

    #[test]
    fn test_decode_entries_complex_transactions() {
        use shredstream_decoder::types::{CompiledInstruction, MessageAddressTableLookup, V0Message};

        let complex_entry = Entry {
            num_hashes: 999,
            hash: Hash::new_from_array([255u8; 32]),
            transactions: vec![
                // Legacy transaction
                VersionedTransaction {
                    signatures: vec![Signature::new_from_array([1u8; 64]), Signature::new_from_array([2u8; 64])],
                    message: VersionedMessage::Legacy(LegacyMessage {
                        header: MessageHeader {
                            num_required_signatures: 2,
                            num_readonly_signed_accounts: 1,
                            num_readonly_unsigned_accounts: 2,
                        },
                        account_keys: vec![
                            Pubkey::new_from_array([10u8; 32]),
                            Pubkey::new_from_array([20u8; 32]),
                            Pubkey::new_from_array([30u8; 32]),
                        ],
                        recent_blockhash: Hash::new_from_array([40u8; 32]),
                        instructions: vec![CompiledInstruction {
                            program_id_index: 0,
                            accounts: vec![1, 2],
                            data: vec![100, 200],
                        }],
                    }),
                },
                // V0 transaction
                VersionedTransaction {
                    signatures: vec![Signature::new_from_array([3u8; 64])],
                    message: VersionedMessage::V0(V0Message {
                        header: MessageHeader {
                            num_required_signatures: 1,
                            num_readonly_signed_accounts: 0,
                            num_readonly_unsigned_accounts: 1,
                        },
                        account_keys: vec![Pubkey::new_from_array([50u8; 32])],
                        recent_blockhash: Hash::new_from_array([60u8; 32]),
                        instructions: vec![CompiledInstruction {
                            program_id_index: 0,
                            accounts: vec![],
                            data: vec![150, 250],
                        }],
                        address_table_lookups: vec![MessageAddressTableLookup {
                            account_key: Pubkey::new_from_array([70u8; 32]),
                            writable_indexes: vec![0, 1],
                            readonly_indexes: vec![2, 3],
                        }],
                    }),
                },
            ],
        };

        let entries = vec![complex_entry.clone()];
        let serialized = bincode::serialize(&entries).unwrap();

        let slot = 777777;
        let result = decode_entries_internal(slot, &serialized);

        assert!(result.is_ok());
        let parsed_entry = result.unwrap();
        assert_eq!(parsed_entry.slot, slot);
        assert_eq!(parsed_entry.entries.len(), 1);
        assert_eq!(parsed_entry.entries[0], complex_entry);
        assert_eq!(parsed_entry.entries[0].transactions.len(), 2);

        // Verify transaction types
        assert!(matches!(parsed_entry.entries[0].transactions[0].message, VersionedMessage::Legacy(_)));
        assert!(matches!(parsed_entry.entries[0].transactions[1].message, VersionedMessage::V0(_)));
    }

    #[test]
    fn test_decode_entries_edge_cases() {
        // Test with various edge case scenarios
        let edge_cases = vec![
            // Empty entries
            vec![],
            // Single entry with no transactions
            vec![Entry::default()],
            // Entry with maximum values
            vec![Entry { num_hashes: u64::MAX, hash: Hash::new_from_array([255u8; 32]), transactions: vec![] }],
            // Entry with zero values
            vec![Entry { num_hashes: 0, hash: Hash::default(), transactions: vec![] }],
        ];

        for (i, entries) in edge_cases.into_iter().enumerate() {
            let serialized = bincode::serialize(&entries).unwrap();
            let slot = (i + 1) as u64 * 1000;

            let result = decode_entries_internal(slot, &serialized);

            assert!(result.is_ok(), "Edge case {} failed", i);
            let parsed_entry = result.unwrap();
            assert_eq!(parsed_entry.slot, slot);
            assert_eq!(parsed_entry.entries.len(), entries.len());
            assert_eq!(parsed_entry.entries, entries);
        }
    }

    #[test]
    fn test_decode_entries_malformed_data_scenarios() {
        let slot = 123;

        // Test various malformed data scenarios
        let malformed_scenarios = vec![
            vec![],                       // Empty data
            vec![0],                      // Single byte
            vec![255],                    // Single max byte
            vec![1, 2, 3],                // Short invalid data
            vec![0; 10],                  // Zero bytes
            vec![255; 10],                // Max bytes
            vec![1, 0, 0, 0, 0, 0, 0, 0], // Partial length
            vec![255, 255, 255, 255],     // Invalid length
        ];

        for (i, data) in malformed_scenarios.into_iter().enumerate() {
            let result = decode_entries_internal(slot, &data);

            if data.is_empty() {
                // Empty data should fail with specific error
                assert!(result.is_err(), "Malformed scenario {} should fail", i);
            } else if data == vec![0; 10] {
                // 10 zero bytes might be valid (empty Vec<Entry> serialization)
                // This is actually valid bincode for empty vector
                // So we don't assert failure here
            } else {
                // Other malformed data should fail
                assert!(result.is_err(), "Malformed scenario {} should fail", i);
            }
        }
    }

    #[test]
    fn test_decode_entries_roundtrip_compatibility() {
        // Test that decode_entries can handle data that was serialized correctly
        let original_entries = vec![
            generate_mock_entry_with_transactions(0),
            generate_mock_entry_with_transactions(1),
            generate_mock_entry_with_transactions(3),
        ];

        let serialized = bincode::serialize(&original_entries).unwrap();
        let slot = 555555;

        let result = decode_entries_internal(slot, &serialized);

        assert!(result.is_ok());
        let parsed_entry = result.unwrap();
        assert_eq!(parsed_entry.slot, slot);
        assert_eq!(parsed_entry.entries.len(), 3);
        assert_eq!(parsed_entry.entries, original_entries);

        // Verify transaction counts
        assert_eq!(parsed_entry.entries[0].transactions.len(), 0);
        assert_eq!(parsed_entry.entries[1].transactions.len(), 1);
        assert_eq!(parsed_entry.entries[2].transactions.len(), 3);
    }
}
