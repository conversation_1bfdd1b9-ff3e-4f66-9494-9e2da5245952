---
applyTo: '**'
---

# Shredstream Decoder - AI Instructions

This document provides comprehensive coding guidelines for AI assistants working on the Shredstream Decoder project.

## Project Overview

The **Shredstream Decoder** is a high-performance Rust library for decoding Solana blockchain shredstream data with WebAssembly (WASM) support. The project enables cross-platform compatibility between Rust and JavaScript/TypeScript environments for processing Solana blockchain entries, transactions, and messages.

### Key Features

-   Solana shredstream data decoding
-   WASM bindings with TypeScript definitions
-   Cross-platform binary compatibility
-   Real-world data collection and testing
-   High-performance parsing of blockchain structures

### Architecture

-   **Core Library**: Rust-based decoder with WASM exports
-   **Data Collector**: Binary tool for gathering real shredstream data
-   **Type System**: Comprehensive Solana data structure definitions
-   **Testing Framework**: Extensive testing with real-world data samples

## Rust Configuration

### Project Settings

-   **Edition**: 2021 (minimum Rust 1.75)
-   **Crate Types**: Both `cdylib` (for WASM) and `rlib` (for Rust)
-   **Formatting**: Use `.rustfmt.toml` configuration (120 char width, Unix newlines)
-   **Linting**: Follow `clippy.toml` settings with strict warnings

### Code Safety and Quality

-   Prefer safe Rust, avoid `unsafe` unless absolutely necessary
-   Use `Result<T, E>` for all fallible operations
-   Handle errors explicitly with meaningful error messages
-   Use `Option<T>` for nullable values
-   Leverage Rust's type system for compile-time safety
-   Follow ownership and borrowing principles strictly

## WASM Integration Guidelines

### TypeScript Compatibility

-   Use `tsify` for automatic TypeScript definition generation
-   Ensure all public structs derive `Tsify` with `into_wasm_abi` and `from_wasm_abi`
-   Return TypeScript-friendly types instead of generic `JsValue`
-   Maintain data structure compatibility between Rust and TypeScript

### Cross-Platform Considerations

-   Encode raw data for cross-platform compatibility (Rust/Node.js)
-   Use `bincode` for serialization/deserialization
-   Test binary compatibility between custom types and official Solana crates
-   Ensure consistent behavior across different platforms

### WASM Build Process

-   Use `wasm-pack build --target bundler --out-dir pkg`
-   Generate TypeScript definitions automatically during build
-   Separate WASM library dependencies from testing dependencies

## Solana Blockchain Integration

### Data Structure Handling

-   Implement custom types that mirror official Solana structures
-   Use `serde` for serialization with appropriate features
-   Handle both Legacy and Versioned message formats
-   Support Solana short-vec encoding for arrays
-   Maintain compatibility with Solana SDK versions

### Shredstream Processing

-   Process raw shred data from Solana streams
-   Handle entry decoding with slot information
-   Support real-time data collection from shredstream endpoints
-   Implement proper error handling for malformed blockchain data

### Message Deserialization

-   Use custom deserializers for complex Solana message formats
-   Handle message version prefixes correctly
-   Support both Legacy and V0 message types
-   Validate message structure during deserialization

## Code Organization

### Module Structure

```
src/
├── lib.rs              # Main library exports and WASM bindings
├── types/              # Solana data structure definitions
│   ├── accounts.rs     # Pubkey and account-related types
│   ├── entries.rs      # Entry and ParsedEntry structures
│   ├── instructions.rs # CompiledInstruction definitions
│   ├── messages.rs     # Message format handling
│   └── transactions.rs # Transaction structures
└── utils/              # Utility functions and helpers
    └── message_deserializer.rs # Custom deserialization logic
```

### Type Definitions

-   Place all type definitions in `src/types/` directory
-   Use meaningful struct names like `ParsedEntry` over generic names
-   Derive necessary traits: `Serialize`, `Deserialize`, `Debug`, `Clone`, `Tsify`
-   Implement `Default` for structures when appropriate
-   Use `#[repr(transparent)]` for wrapper types like `Pubkey`

### Utility Functions

-   Separate complex logic into `src/utils/` directory
-   Create focused, single-responsibility functions
-   Avoid code duplication by extracting common functionality
-   Use descriptive function names that indicate purpose

## Testing Strategy

### Test Organization

```
tests/
├── common/             # Shared testing utilities
│   ├── assertions.rs   # Custom assertion macros
│   ├── fixtures.rs     # Test data fixtures
│   └── mock_data.rs    # Mock data generators
├── data/               # Real-world test data
│   └── shred_*.bin     # Collected shredstream samples
└── [module]_test.rs    # Tests mirroring src/ structure
```

### Testing Principles

-   Test with real-world data samples (1000+ shred files)
-   Implement binary compatibility testing with official Solana crates
-   Use dynamic file discovery instead of hardcoded counts
-   Test small, independent structs before complex dependent structures
-   Separate testing dependencies from library dependencies

### Data Collection and Validation

-   Collect real shred data using the `shred_collector` binary
-   Test decoder accuracy with comprehensive data comparison
-   Validate cross-platform compatibility with collected samples
-   Use environment variables for configurable test parameters

## Development Workflow

### Build Commands (via Makefile)

-   `make build` - Debug build
-   `make build-release` - Optimized build
-   `make build-wasm` - WASM package with TypeScript definitions
-   `make test` - Run basic tests
-   `make test-real-world` - Test with real shredstream data
-   `make collect-shreds` - Gather test data from shredstream

### Code Quality

-   Use `make fmt` for consistent formatting
-   Use `make lint` for clippy checks with strict warnings
-   Follow DRY principle - extract repeated code into functions
-   Maintain clean, readable code without unnecessary comments

### Environment Configuration

-   Use `.env` file for environment-specific settings
-   Support configurable parameters with sensible defaults
-   Use `dotenvy` for environment variable loading
-   Document required environment variables

## Performance Considerations

### Optimization Guidelines

-   Use appropriate data structures for specific use cases
-   Avoid unnecessary allocations in hot paths
-   Leverage iterators efficiently
-   Consider zero-copy operations when possible
-   Profile before optimizing

### Memory Management

-   Understand ownership and borrowing patterns
-   Use `Arc<T>` for shared ownership when needed
-   Avoid memory leaks with proper cleanup
-   Use `Box<T>` for heap allocation only when necessary

## Implementation Rules

### Scope Limitation

-   Implement only what is explicitly requested
-   Avoid adding features beyond requirements
-   Ask before implementing related improvements
-   Focus on minimal viable implementation

### Default Exclusions

Unless explicitly requested, do not add:

-   Input validation and parameter checking
-   Documentation or comments in source code
-   Example implementations
-   Comprehensive error handling beyond basic needs

### Code Style

-   Never generate comments in source code by default
-   Use self-documenting code through clear naming
-   Keep functions focused on single responsibility
-   Use meaningful variable and function names
-   Maintain consistent code organization across modules

## Dependencies and Features

### Core Dependencies

-   `serde` with derive features for serialization
-   `bincode` for binary serialization
-   `wasm-bindgen` with serde-serialize features
-   `tsify` for TypeScript definition generation
-   Solana crates with minimal features enabled

### Optional Features

-   `tokio` for async runtime (collector binary only)
-   `solana-stream-sdk` for shredstream connectivity
-   `dotenvy` for environment variable loading

### Development Dependencies

-   Official Solana crates for compatibility testing
-   Testing utilities for comprehensive validation
-   Mock data generation tools

This document ensures consistent, high-quality code that aligns with the project's goals of cross-platform Solana data processing with WASM support.
